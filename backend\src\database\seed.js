const { User, Category } = require('../models');
const logger = require('../utils/logger');

const seed = async () => {
  try {
    logger.info('Iniciando seed do banco de dados...');

    // Criar usuário administrador padrão
    const adminExists = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!adminExists) {
      await User.create({
        name: 'Administrador',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      });
      logger.info('Usuário administrador criado: <EMAIL> / admin123');
    }

    // Criar categorias padrão
    const defaultCategories = [
      {
        name: 'Política',
        slug: 'politica',
        description: 'Notícias sobre política nacional e internacional',
        color: '#FF6B6B',
        sortOrder: 1
      },
      {
        name: 'Economia',
        slug: 'economia',
        description: 'Notícias sobre economia, mercado financeiro e negócios',
        color: '#4ECDC4',
        sortOrder: 2
      },
      {
        name: 'Esportes',
        slug: 'esportes',
        description: 'Notícias esportivas e resultados',
        color: '#45B7D1',
        sortOrder: 3
      },
      {
        name: 'Tecnologia',
        slug: 'tecnologia',
        description: 'Notícias sobre tecnologia, inovação e ciência',
        color: '#96CEB4',
        sortOrder: 4
      },
      {
        name: 'Entretenimento',
        slug: 'entretenimento',
        description: 'Notícias sobre cinema, música, TV e celebridades',
        color: '#FFEAA7',
        sortOrder: 5
      },
      {
        name: 'Saúde',
        slug: 'saude',
        description: 'Notícias sobre saúde, medicina e bem-estar',
        color: '#DDA0DD',
        sortOrder: 6
      }
    ];

    for (const categoryData of defaultCategories) {
      const categoryExists = await Category.findOne({ where: { slug: categoryData.slug } });
      if (!categoryExists) {
        await Category.create(categoryData);
        logger.info(`Categoria criada: ${categoryData.name}`);
      }
    }

    logger.info('Seed concluído com sucesso!');
    process.exit(0);
  } catch (error) {
    logger.error('Erro no seed:', { error: error.message });
    process.exit(1);
  }
};

// Executar seed se chamado diretamente
if (require.main === module) {
  seed();
}

module.exports = seed;
