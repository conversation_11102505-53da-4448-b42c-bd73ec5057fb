import { Outlet, <PERSON> } from 'react-router-dom'
import { useQuery } from 'react-query'
import { categoryService } from '../services/categoryService'
import { Search, Menu, X } from 'lucide-react'
import { useState } from 'react'

const PublicLayout = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const { data: categoriesData } = useQuery(
    'public-categories',
    () => categoryService.getCategories({ active: true, limit: 10 })
  )

  const categories = categoriesData?.data?.categories || []

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link to="/public" className="text-2xl font-bold text-primary-600">
                Portal News
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8">
              <Link
                to="/public"
                className="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"
              >
                Início
              </Link>
              {categories.slice(0, 5).map((category) => (
                <Link
                  key={category.id}
                  to={`/public/category/${category.slug}`}
                  className="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"
                >
                  {category.name}
                </Link>
              ))}
            </nav>

            {/* Search and Mobile Menu */}
            <div className="flex items-center space-x-4">
              <button className="text-gray-400 hover:text-gray-600">
                <Search className="h-5 w-5" />
              </button>
              
              <button
                className="md:hidden text-gray-400 hover:text-gray-600"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50">
              <Link
                to="/public"
                className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                Início
              </Link>
              {categories.map((category) => (
                <Link
                  key={category.id}
                  to={`/public/category/${category.slug}`}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </header>

      {/* Main Content */}
      <main>
        <Outlet />
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-lg font-bold mb-4">Portal News</h3>
              <p className="text-gray-300 mb-4">
                Seu portal de notícias confiável, trazendo as informações mais relevantes 
                e atualizadas sobre política, economia, esportes, tecnologia e muito mais.
              </p>
            </div>
            
            <div>
              <h4 className="text-md font-semibold mb-4">Categorias</h4>
              <ul className="space-y-2">
                {categories.slice(0, 6).map((category) => (
                  <li key={category.id}>
                    <Link
                      to={`/public/category/${category.slug}`}
                      className="text-gray-300 hover:text-white"
                    >
                      {category.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h4 className="text-md font-semibold mb-4">Links</h4>
              <ul className="space-y-2">
                <li>
                  <Link to="/public" className="text-gray-300 hover:text-white">
                    Início
                  </Link>
                </li>
                <li>
                  <Link to="/login" className="text-gray-300 hover:text-white">
                    Área Administrativa
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <p className="text-gray-300">
              © 2024 Portal News. Todos os direitos reservados.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default PublicLayout
