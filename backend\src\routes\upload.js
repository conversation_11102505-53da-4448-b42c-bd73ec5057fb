const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { authenticateToken, requireEditor } = require('../middleware/auth');
const { upload, processImage } = require('../middleware/upload');

// Rota protegida para upload de imagens
router.post('/image', authenticateToken, requireEditor, upload, processImage, uploadController.uploadImage);

module.exports = router;
