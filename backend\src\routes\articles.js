const express = require('express');
const router = express.Router();
const articleController = require('../controllers/articleController');
const { authenticateToken, requireEditor } = require('../middleware/auth');
const { validateArticle, validateUUID, validatePagination } = require('../middleware/validation');

// Middleware opcional para autenticação (permite acesso público e autenticado)
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  
  if (token) {
    authenticateToken(req, res, next);
  } else {
    next();
  }
};

// Rotas públicas e com autenticação opcional
router.get('/', validatePagination, optionalAuth, articleController.getArticles);
router.get('/slug/:slug', optionalAuth, articleController.getArticleBySlug);
router.get('/:id', validateUUID, optionalAuth, articleController.getArticleById);

// Rotas protegidas
router.post('/', authenticateToken, requireEditor, validateArticle, articleController.createArticle);
router.put('/:id', authenticateToken, requireEditor, validateUUID, validateArticle, articleController.updateArticle);
router.delete('/:id', authenticateToken, requireEditor, validateUUID, articleController.deleteArticle);

module.exports = router;
