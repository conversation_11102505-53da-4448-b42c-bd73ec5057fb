const { Category, Article } = require('../models');
const { Op } = require('sequelize');

const getCategories = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, active } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    
    if (search) {
      whereClause.name = {
        [Op.iLike]: `%${search}%`
      };
    }

    if (active !== undefined) {
      whereClause.isActive = active === 'true';
    }

    const { count, rows: categories } = await Category.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['sortOrder', 'ASC'], ['name', 'ASC']],
      include: [
        {
          model: Article,
          as: 'articles',
          attributes: ['id'],
          where: { status: 'published' },
          required: false
        }
      ]
    });

    // Adicionar contagem de artigos
    const categoriesWithCount = categories.map(category => ({
      ...category.toJSON(),
      articlesCount: category.articles ? category.articles.length : 0,
      articles: undefined
    }));

    res.json({
      success: true,
      data: {
        categories: categoriesWithCount,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erro ao buscar categorias:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;

    const category = await Category.findByPk(id, {
      include: [
        {
          model: Article,
          as: 'articles',
          attributes: ['id', 'title', 'slug', 'status', 'publishedAt'],
          where: { status: 'published' },
          required: false,
          limit: 10,
          order: [['publishedAt', 'DESC']]
        }
      ]
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Categoria não encontrada'
      });
    }

    res.json({
      success: true,
      data: {
        category
      }
    });
  } catch (error) {
    console.error('Erro ao buscar categoria:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const createCategory = async (req, res) => {
  try {
    const { name, slug, description, color, isActive, sortOrder } = req.body;

    // Verificar se já existe categoria com mesmo nome ou slug
    const existingCategory = await Category.findOne({
      where: {
        [Op.or]: [
          { name },
          { slug: slug || name.toLowerCase().replace(/[^a-z0-9]/g, '-') }
        ]
      }
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Já existe uma categoria com este nome ou slug'
      });
    }

    const category = await Category.create({
      name,
      slug,
      description,
      color,
      isActive,
      sortOrder
    });

    res.status(201).json({
      success: true,
      message: 'Categoria criada com sucesso',
      data: {
        category
      }
    });
  } catch (error) {
    console.error('Erro ao criar categoria:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, slug, description, color, isActive, sortOrder } = req.body;

    const category = await Category.findByPk(id);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Categoria não encontrada'
      });
    }

    // Verificar se já existe categoria com mesmo nome ou slug (exceto a atual)
    if (name || slug) {
      const whereClause = {
        id: { [Op.ne]: id }
      };

      if (name && name !== category.name) {
        whereClause[Op.or] = whereClause[Op.or] || [];
        whereClause[Op.or].push({ name });
      }

      if (slug && slug !== category.slug) {
        whereClause[Op.or] = whereClause[Op.or] || [];
        whereClause[Op.or].push({ slug });
      }

      if (whereClause[Op.or]) {
        const existingCategory = await Category.findOne({ where: whereClause });
        if (existingCategory) {
          return res.status(400).json({
            success: false,
            message: 'Já existe uma categoria com este nome ou slug'
          });
        }
      }
    }

    await category.update({
      name: name || category.name,
      slug: slug || category.slug,
      description: description !== undefined ? description : category.description,
      color: color !== undefined ? color : category.color,
      isActive: isActive !== undefined ? isActive : category.isActive,
      sortOrder: sortOrder !== undefined ? sortOrder : category.sortOrder
    });

    res.json({
      success: true,
      message: 'Categoria atualizada com sucesso',
      data: {
        category
      }
    });
  } catch (error) {
    console.error('Erro ao atualizar categoria:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;

    const category = await Category.findByPk(id);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Categoria não encontrada'
      });
    }

    // Verificar se há artigos associados
    const articlesCount = await Article.count({
      where: { categoryId: id }
    });

    if (articlesCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Não é possível deletar a categoria. Existem ${articlesCount} artigo(s) associado(s).`
      });
    }

    await category.destroy();

    res.json({
      success: true,
      message: 'Categoria deletada com sucesso'
    });
  } catch (error) {
    console.error('Erro ao deletar categoria:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

module.exports = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
};
