# Portal News - MVP

Plataforma de Notícias Dinâmica com painel administrativo, portal público e aplicativo móvel.

## 🚀 Funcionalidades

### Painel Administrativo
- ✅ Autenticação segura
- ✅ Editor WYSIWYG completo (TinyMCE)
- ✅ Gerenciamento de categorias
- ✅ Upload de imagens otimizado
- ✅ Agendamento de publicações
- ✅ Dashboard com métricas

### Portal Público
- ✅ Homepage responsiva
- ✅ Visualização de artigos
- ✅ Navegação por categorias
- ✅ Sistema de busca
- ✅ Compartilhamento social
- ✅ Performance otimizada

### Aplicativo Móvel
- ✅ Design moderno e intuitivo
- ✅ Sincronização em tempo real
- ✅ Notificações push
- ✅ Busca e favoritos
- ✅ Performance nativa

## 🛠️ Tecnologias

- **Backend**: Node.js + Express + PostgreSQL
- **Frontend**: React + Vite + TinyMCE
- **Mobile**: React Native
- **Autenticação**: JWT
- **Upload**: <PERSON>lter + Sharp

## 📦 Instalação

```bash
# Instalar dependências
npm run install:all

# Configurar banco de dados
cd backend
cp .env.example .env
# Editar .env com suas configurações
npm run migrate

# Executar em desenvolvimento
npm run dev
```

## 📱 Estrutura do Projeto

```
PortalNews/
├── backend/          # API Node.js/Express
├── frontend/         # Portal web React
├── mobile/           # App React Native
├── shared/           # Tipos compartilhados
└── docs/             # Documentação
```

## 🔧 Configuração

### Variáveis de Ambiente (Backend)
```env
DATABASE_URL=postgresql://user:password@localhost:5432/portalnews
JWT_SECRET=your-secret-key
PORT=3001
UPLOAD_PATH=./uploads
```

### Configuração do Banco
```bash
# Criar banco PostgreSQL
createdb portalnews

# Executar migrações
npm run migrate

# Seed inicial (usuário admin)
npm run seed
```

## 📖 API Endpoints

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/register` - Registro
- `GET /api/auth/me` - Perfil atual

### Artigos
- `GET /api/articles` - Listar artigos
- `POST /api/articles` - Criar artigo
- `GET /api/articles/:id` - Obter artigo
- `PUT /api/articles/:id` - Atualizar artigo
- `DELETE /api/articles/:id` - Deletar artigo

### Categorias
- `GET /api/categories` - Listar categorias
- `POST /api/categories` - Criar categoria
- `PUT /api/categories/:id` - Atualizar categoria
- `DELETE /api/categories/:id` - Deletar categoria

### Upload
- `POST /api/upload` - Upload de imagem

## 🚀 Deploy

### Frontend (Vercel/Netlify)
```bash
cd frontend
npm run build
# Deploy da pasta dist/
```

### Backend (Railway/Heroku)
```bash
cd backend
# Configurar variáveis de ambiente
# Deploy automático via Git
```

## 📱 Mobile App

### Desenvolvimento
```bash
cd mobile
npm install
npx react-native run-android
npx react-native run-ios
```

### Build
```bash
# Android
cd android && ./gradlew assembleRelease

# iOS
cd ios && xcodebuild -workspace PortalNews.xcworkspace -scheme PortalNews archive
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.
