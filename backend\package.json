{"name": "portal-news-backend", "version": "1.0.0", "description": "Backend API para Portal de Notícias", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "sequelize": "^6.35.2", "sharp": "^0.33.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["api", "news", "portal", "cms"], "author": "Portal News Team", "license": "MIT"}