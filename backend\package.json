{"name": "portal-news-backend", "version": "1.0.0", "description": "Backend API para Portal de Notícias", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["api", "news", "portal", "cms"], "author": "Portal News Team", "license": "MIT"}