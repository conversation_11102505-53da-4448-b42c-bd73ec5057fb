const express = require('express');
const router = express.Router();

// Importar rotas
const authRoutes = require('./auth');
const categoryRoutes = require('./categories');
const articleRoutes = require('./articles');
const uploadRoutes = require('./upload');

// Configurar rotas
router.use('/auth', authRoutes);
router.use('/categories', categoryRoutes);
router.use('/articles', articleRoutes);
router.use('/upload', uploadRoutes);

// Rota de health check
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API Portal News funcionando',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
