import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import Layout from './components/Layout'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Articles from './pages/Articles'
import ArticleForm from './pages/ArticleForm'
import Categories from './pages/Categories'
import Profile from './pages/Profile'
import PublicLayout from './components/PublicLayout'
import Home from './pages/public/Home'
import ArticleView from './pages/public/ArticleView'
import CategoryView from './pages/public/CategoryView'
import LoadingSpinner from './components/LoadingSpinner'

function App() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <Routes>
      {/* Rotas públicas */}
      <Route path="/public" element={<PublicLayout />}>
        <Route index element={<Home />} />
        <Route path="article/:slug" element={<ArticleView />} />
        <Route path="category/:slug" element={<CategoryView />} />
      </Route>

      {/* Rotas de autenticação */}
      <Route 
        path="/login" 
        element={user ? <Navigate to="/admin" replace /> : <Login />} 
      />

      {/* Rotas administrativas protegidas */}
      <Route 
        path="/admin" 
        element={user ? <Layout /> : <Navigate to="/login" replace />}
      >
        <Route index element={<Dashboard />} />
        <Route path="articles" element={<Articles />} />
        <Route path="articles/new" element={<ArticleForm />} />
        <Route path="articles/edit/:id" element={<ArticleForm />} />
        <Route path="categories" element={<Categories />} />
        <Route path="profile" element={<Profile />} />
      </Route>

      {/* Redirecionamento padrão */}
      <Route path="/" element={<Navigate to="/public" replace />} />
      <Route path="*" element={<Navigate to="/public" replace />} />
    </Routes>
  )
}

export default App
