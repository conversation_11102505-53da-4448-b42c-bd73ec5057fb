const { Article, Category, User } = require('../models');
const { Op } = require('sequelize');

const getArticles = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      category, 
      status, 
      author,
      featured,
      breaking 
    } = req.query;
    
    const offset = (page - 1) * limit;
    const whereClause = {};

    // Filtros
    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { content: { [Op.iLike]: `%${search}%` } },
        { summary: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (category) {
      whereClause.categoryId = category;
    }

    if (status) {
      whereClause.status = status;
    } else if (!req.user) {
      // Para usuários não autenticados, mostrar apenas publicados
      whereClause.status = 'published';
    }

    if (author) {
      whereClause.authorId = author;
    }

    if (featured !== undefined) {
      whereClause.isFeatured = featured === 'true';
    }

    if (breaking !== undefined) {
      whereClause.isBreaking = breaking === 'true';
    }

    const { count, rows: articles } = await Article.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['publishedAt', 'DESC'], ['createdAt', 'DESC']],
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'color']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    });

    res.json({
      success: true,
      data: {
        articles,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erro ao buscar artigos:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const getArticleById = async (req, res) => {
  try {
    const { id } = req.params;

    const article = await Article.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'color']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    });

    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Artigo não encontrado'
      });
    }

    // Se não for o autor ou admin, só mostrar artigos publicados
    if (!req.user || (req.user.id !== article.authorId && req.user.role !== 'admin')) {
      if (article.status !== 'published') {
        return res.status(404).json({
          success: false,
          message: 'Artigo não encontrado'
        });
      }
    }

    // Incrementar visualizações se for acesso público
    if (!req.user || req.user.id !== article.authorId) {
      await article.increment('views');
    }

    res.json({
      success: true,
      data: {
        article
      }
    });
  } catch (error) {
    console.error('Erro ao buscar artigo:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const getArticleBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const article = await Article.findOne({
      where: { slug },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'color']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    });

    if (!article || article.status !== 'published') {
      return res.status(404).json({
        success: false,
        message: 'Artigo não encontrado'
      });
    }

    // Incrementar visualizações
    await article.increment('views');

    res.json({
      success: true,
      data: {
        article
      }
    });
  } catch (error) {
    console.error('Erro ao buscar artigo:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const createArticle = async (req, res) => {
  try {
    const {
      title,
      slug,
      subtitle,
      summary,
      content,
      categoryId,
      featuredImage,
      featuredImageAlt,
      status,
      scheduledAt,
      metaTitle,
      metaDescription,
      tags,
      isFeatured,
      isBreaking
    } = req.body;

    // Verificar se categoria existe
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Categoria não encontrada'
      });
    }

    // Verificar se slug já existe
    if (slug) {
      const existingArticle = await Article.findOne({ where: { slug } });
      if (existingArticle) {
        return res.status(400).json({
          success: false,
          message: 'Já existe um artigo com este slug'
        });
      }
    }

    const article = await Article.create({
      title,
      slug,
      subtitle,
      summary,
      content,
      categoryId,
      authorId: req.user.id,
      featuredImage,
      featuredImageAlt,
      status: status || 'draft',
      scheduledAt,
      metaTitle,
      metaDescription,
      tags: tags || [],
      isFeatured: isFeatured || false,
      isBreaking: isBreaking || false
    });

    // Buscar artigo com relacionamentos
    const createdArticle = await Article.findByPk(article.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'color']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Artigo criado com sucesso',
      data: {
        article: createdArticle
      }
    });
  } catch (error) {
    console.error('Erro ao criar artigo:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const updateArticle = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      slug,
      subtitle,
      summary,
      content,
      categoryId,
      featuredImage,
      featuredImageAlt,
      status,
      scheduledAt,
      metaTitle,
      metaDescription,
      tags,
      isFeatured,
      isBreaking
    } = req.body;

    const article = await Article.findByPk(id);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Artigo não encontrado'
      });
    }

    // Verificar permissões
    if (req.user.role !== 'admin' && article.authorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado'
      });
    }

    // Verificar se categoria existe
    if (categoryId && categoryId !== article.categoryId) {
      const category = await Category.findByPk(categoryId);
      if (!category) {
        return res.status(400).json({
          success: false,
          message: 'Categoria não encontrada'
        });
      }
    }

    // Verificar se slug já existe
    if (slug && slug !== article.slug) {
      const existingArticle = await Article.findOne({
        where: {
          slug,
          id: { [Op.ne]: id }
        }
      });
      if (existingArticle) {
        return res.status(400).json({
          success: false,
          message: 'Já existe um artigo com este slug'
        });
      }
    }

    await article.update({
      title: title || article.title,
      slug: slug || article.slug,
      subtitle: subtitle !== undefined ? subtitle : article.subtitle,
      summary: summary !== undefined ? summary : article.summary,
      content: content || article.content,
      categoryId: categoryId || article.categoryId,
      featuredImage: featuredImage !== undefined ? featuredImage : article.featuredImage,
      featuredImageAlt: featuredImageAlt !== undefined ? featuredImageAlt : article.featuredImageAlt,
      status: status || article.status,
      scheduledAt: scheduledAt !== undefined ? scheduledAt : article.scheduledAt,
      metaTitle: metaTitle !== undefined ? metaTitle : article.metaTitle,
      metaDescription: metaDescription !== undefined ? metaDescription : article.metaDescription,
      tags: tags !== undefined ? tags : article.tags,
      isFeatured: isFeatured !== undefined ? isFeatured : article.isFeatured,
      isBreaking: isBreaking !== undefined ? isBreaking : article.isBreaking
    });

    // Buscar artigo atualizado com relacionamentos
    const updatedArticle = await Article.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'color']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'name', 'email', 'avatar']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Artigo atualizado com sucesso',
      data: {
        article: updatedArticle
      }
    });
  } catch (error) {
    console.error('Erro ao atualizar artigo:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

const deleteArticle = async (req, res) => {
  try {
    const { id } = req.params;

    const article = await Article.findByPk(id);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Artigo não encontrado'
      });
    }

    // Verificar permissões
    if (req.user.role !== 'admin' && article.authorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado'
      });
    }

    await article.destroy();

    res.json({
      success: true,
      message: 'Artigo deletado com sucesso'
    });
  } catch (error) {
    console.error('Erro ao deletar artigo:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

module.exports = {
  getArticles,
  getArticleById,
  getArticleBySlug,
  createArticle,
  updateArticle,
  deleteArticle
};
