const uploadImage = async (req, res) => {
  try {
    if (!req.uploadedFile) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo foi enviado'
      });
    }

    res.json({
      success: true,
      message: 'Imagem enviada com sucesso',
      data: {
        file: {
          filename: req.uploadedFile.filename,
          originalName: req.uploadedFile.originalName,
          mimetype: req.uploadedFile.mimetype,
          size: req.uploadedFile.size,
          url: req.uploadedFile.url
        }
      }
    });
  } catch (error) {
    console.error('Erro no upload:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
};

module.exports = {
  uploadImage
};
