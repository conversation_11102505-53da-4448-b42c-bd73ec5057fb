import api from './api'

class ArticleService {
  async getArticles(params = {}) {
    const response = await api.get('/articles', { params })
    return response
  }

  async getArticleById(id) {
    const response = await api.get(`/articles/${id}`)
    return response.data.article
  }

  async getArticleBySlug(slug) {
    const response = await api.get(`/articles/slug/${slug}`)
    return response.data.article
  }

  async createArticle(articleData) {
    const response = await api.post('/articles', articleData)
    return response
  }

  async updateArticle(id, articleData) {
    const response = await api.put(`/articles/${id}`, articleData)
    return response
  }

  async deleteArticle(id) {
    const response = await api.delete(`/articles/${id}`)
    return response
  }

  async uploadImage(file) {
    const formData = new FormData()
    formData.append('image', file)
    
    const response = await api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response
  }
}

export const articleService = new ArticleService()
