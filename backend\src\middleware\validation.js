const { body, param, query, validationResult } = require('express-validator');

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '<PERSON><PERSON> inválidos',
      errors: errors.array()
    });
  }
  next();
};

// User validation rules
const validateUserRegistration = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Senha deve ter pelo menos 6 caracteres'),
  body('role')
    .optional()
    .isIn(['admin', 'editor'])
    .withMessage('Role deve ser admin ou editor'),
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .notEmpty()
    .withMessage('Senha é obrigatória'),
  handleValidationErrors
];

// Category validation rules
const validateCategory = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Nome deve ter entre 2 e 50 caracteres'),
  body('slug')
    .optional()
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug deve conter apenas letras minúsculas, números e hífens'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Descrição deve ter no máximo 500 caracteres'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Cor deve estar no formato hexadecimal (#RRGGBB)'),
  handleValidationErrors
];

// Article validation rules
const validateArticle = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Título deve ter entre 5 e 200 caracteres'),
  body('slug')
    .optional()
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Slug deve conter apenas letras minúsculas, números e hífens'),
  body('subtitle')
    .optional()
    .isLength({ max: 300 })
    .withMessage('Subtítulo deve ter no máximo 300 caracteres'),
  body('summary')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Resumo deve ter no máximo 500 caracteres'),
  body('content')
    .notEmpty()
    .withMessage('Conteúdo é obrigatório'),
  body('categoryId')
    .isUUID()
    .withMessage('ID da categoria deve ser um UUID válido'),
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('Status deve ser draft, published ou archived'),
  body('metaTitle')
    .optional()
    .isLength({ max: 60 })
    .withMessage('Meta título deve ter no máximo 60 caracteres'),
  body('metaDescription')
    .optional()
    .isLength({ max: 160 })
    .withMessage('Meta descrição deve ter no máximo 160 caracteres'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags devem ser um array'),
  handleValidationErrors
];

// Common validation rules
const validateUUID = [
  param('id')
    .isUUID()
    .withMessage('ID deve ser um UUID válido'),
  handleValidationErrors
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Página deve ser um número inteiro maior que 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite deve ser um número entre 1 e 100'),
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateUserRegistration,
  validateUserLogin,
  validateCategory,
  validateArticle,
  validateUUID,
  validatePagination
};
