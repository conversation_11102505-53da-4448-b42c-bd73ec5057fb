import { useQuery } from 'react-query'
import { Link } from 'react-router-dom'
import { articleService } from '../../services/articleService'
import { categoryService } from '../../services/categoryService'
import { Clock, Eye, User } from 'lucide-react'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import LoadingSpinner from '../../components/LoadingSpinner'

const Home = () => {
  const { data: featuredData, isLoading: featuredLoading } = useQuery(
    'featured-articles',
    () => articleService.getArticles({ featured: true, limit: 3, status: 'published' })
  )

  const { data: latestData, isLoading: latestLoading } = useQuery(
    'latest-articles',
    () => articleService.getArticles({ limit: 12, status: 'published' })
  )

  const { data: categoriesData } = useQuery(
    'home-categories',
    () => categoryService.getCategories({ active: true, limit: 6 })
  )

  const featuredArticles = featuredData?.data?.articles || []
  const latestArticles = latestData?.data?.articles || []
  const categories = categoriesData?.data?.categories || []

  if (featuredLoading || latestLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Hero Section - Featured Articles */}
      {featuredArticles.length > 0 && (
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Destaques</h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {featuredArticles.map((article, index) => (
              <article
                key={article.id}
                className={`group cursor-pointer ${
                  index === 0 ? 'lg:col-span-2 lg:row-span-2' : ''
                }`}
              >
                <Link to={`/public/article/${article.slug}`}>
                  <div className="relative overflow-hidden rounded-lg bg-gray-200 aspect-video">
                    {article.featuredImage ? (
                      <img
                        src={article.featuredImage}
                        alt={article.featuredImageAlt || article.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                        <span className="text-white text-lg font-medium">
                          {article.title.charAt(0)}
                        </span>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <span
                          className="px-2 py-1 text-xs font-medium text-white rounded"
                          style={{ backgroundColor: article.category?.color || '#6B7280' }}
                        >
                          {article.category?.name}
                        </span>
                        {article.isBreaking && (
                          <span className="px-2 py-1 text-xs font-medium bg-red-600 text-white rounded">
                            URGENTE
                          </span>
                        )}
                      </div>
                      <h3 className={`text-white font-bold group-hover:text-primary-200 transition-colors ${
                        index === 0 ? 'text-xl lg:text-2xl' : 'text-lg'
                      }`}>
                        {article.title}
                      </h3>
                      {article.subtitle && index === 0 && (
                        <p className="text-gray-200 text-sm mt-2 line-clamp-2">
                          {article.subtitle}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 mt-3 text-gray-300 text-sm">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" />
                          <span>
                            {format(new Date(article.publishedAt), 'dd/MM/yyyy', { locale: ptBR })}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Eye className="w-4 h-4" />
                          <span>{article.views || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </article>
            ))}
          </div>
        </section>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Latest Articles */}
        <section className="lg:col-span-3">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Últimas Notícias</h2>
          <div className="space-y-6">
            {latestArticles.map((article) => (
              <article key={article.id} className="group">
                <Link to={`/public/article/${article.slug}`}>
                  <div className="flex space-x-4">
                    <div className="flex-shrink-0 w-32 h-24 bg-gray-200 rounded-lg overflow-hidden">
                      {article.featuredImage ? (
                        <img
                          src={article.featuredImage}
                          alt={article.featuredImageAlt || article.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                          <span className="text-white font-medium">
                            {article.title.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <span
                          className="px-2 py-1 text-xs font-medium text-white rounded"
                          style={{ backgroundColor: article.category?.color || '#6B7280' }}
                        >
                          {article.category?.name}
                        </span>
                        {article.isBreaking && (
                          <span className="px-2 py-1 text-xs font-medium bg-red-600 text-white rounded">
                            URGENTE
                          </span>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors line-clamp-2">
                        {article.title}
                      </h3>
                      {article.summary && (
                        <p className="text-gray-600 text-sm mt-2 line-clamp-2">
                          {article.summary}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 mt-3 text-gray-500 text-sm">
                        <div className="flex items-center space-x-1">
                          <User className="w-4 h-4" />
                          <span>{article.author?.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" />
                          <span>
                            {format(new Date(article.publishedAt), 'dd/MM/yyyy', { locale: ptBR })}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Eye className="w-4 h-4" />
                          <span>{article.views || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </article>
            ))}
          </div>
        </section>

        {/* Sidebar */}
        <aside className="space-y-8">
          {/* Categories */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Categorias</h3>
            <div className="space-y-2">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  to={`/public/category/${category.slug}`}
                  className="flex items-center space-x-3 p-2 rounded-md hover:bg-white transition-colors"
                >
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <span className="text-gray-700 hover:text-primary-600">
                    {category.name}
                  </span>
                  <span className="text-gray-500 text-sm ml-auto">
                    {category.articlesCount || 0}
                  </span>
                </Link>
              ))}
            </div>
          </div>

          {/* Newsletter */}
          <div className="bg-primary-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Newsletter</h3>
            <p className="text-gray-600 text-sm mb-4">
              Receba as principais notícias diretamente no seu email.
            </p>
            <form className="space-y-3">
              <input
                type="email"
                placeholder="Seu email"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <button
                type="submit"
                className="w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 transition-colors"
              >
                Inscrever-se
              </button>
            </form>
          </div>
        </aside>
      </div>
    </div>
  )
}

export default Home
