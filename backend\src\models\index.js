const sequelize = require('../config/database');
const User = require('./User');
const Category = require('./Category');
const Article = require('./Article');

// Define associations
User.hasMany(Article, {
  foreignKey: 'authorId',
  as: 'articles'
});

Article.belongsTo(User, {
  foreignKey: 'authorId',
  as: 'author'
});

Category.hasMany(Article, {
  foreignKey: 'categoryId',
  as: 'articles'
});

Article.belongsTo(Category, {
  foreignKey: 'categoryId',
  as: 'category'
});

// Article-Category many-to-many relationship for multiple categories
const ArticleCategory = sequelize.define('ArticleCategory', {}, {
  tableName: 'article_categories',
  timestamps: false
});

Article.belongsToMany(Category, {
  through: ArticleCategory,
  as: 'categories',
  foreignKey: 'articleId'
});

Category.belongsToMany(Article, {
  through: ArticleCategory,
  as: 'categoryArticles',
  foreignKey: 'categoryId'
});

module.exports = {
  sequelize,
  User,
  Category,
  Article,
  ArticleCategory
};
