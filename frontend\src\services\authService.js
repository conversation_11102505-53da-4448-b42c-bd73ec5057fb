import api from './api'

class AuthService {
  constructor() {
    this.token = null
  }

  setToken(token) {
    this.token = token
  }

  async login(email, password) {
    const response = await api.post('/auth/login', { email, password })
    return response
  }

  async register(userData) {
    const response = await api.post('/auth/register', userData)
    return response
  }

  async getProfile() {
    const response = await api.get('/auth/me')
    return response.data.user
  }

  async updateProfile(profileData) {
    const response = await api.put('/auth/profile', profileData)
    return response
  }

  async changePassword(currentPassword, newPassword) {
    const response = await api.put('/auth/password', {
      currentPassword,
      newPassword
    })
    return response
  }
}

export const authService = new AuthService()
