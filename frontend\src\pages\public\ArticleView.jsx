import { useParams } from 'react-router-dom'
import { useQuery } from 'react-query'
import { articleService } from '../../services/articleService'
import { Clock, Eye, User, Share2, Facebook, Twitter, Linkedin } from 'lucide-react'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import LoadingSpinner from '../../components/LoadingSpinner'

const ArticleView = () => {
  const { slug } = useParams()

  const { data: article, isLoading, error } = useQuery(
    ['article', slug],
    () => articleService.getArticleBySlug(slug),
    { enabled: !!slug }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !article) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Artigo não encontrado</h1>
        <p className="text-gray-600">O artigo que você está procurando não existe ou foi removido.</p>
      </div>
    )
  }

  const shareUrl = window.location.href
  const shareText = `${article.title} - Portal News`

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`
  }

  return (
    <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Article Header */}
      <header className="mb-8">
        <div className="flex items-center space-x-2 mb-4">
          <span
            className="px-3 py-1 text-sm font-medium text-white rounded-full"
            style={{ backgroundColor: article.category?.color || '#6B7280' }}
          >
            {article.category?.name}
          </span>
          {article.isBreaking && (
            <span className="px-3 py-1 text-sm font-medium bg-red-600 text-white rounded-full">
              URGENTE
            </span>
          )}
          {article.isFeatured && (
            <span className="px-3 py-1 text-sm font-medium bg-yellow-500 text-white rounded-full">
              DESTAQUE
            </span>
          )}
        </div>

        <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          {article.title}
        </h1>

        {article.subtitle && (
          <h2 className="text-xl text-gray-600 mb-6">
            {article.subtitle}
          </h2>
        )}

        <div className="flex flex-wrap items-center justify-between gap-4 py-4 border-t border-b border-gray-200">
          <div className="flex items-center space-x-6 text-gray-500">
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span className="text-sm">{article.author?.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span className="text-sm">
                {format(new Date(article.publishedAt), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR })}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <span className="text-sm">{article.views || 0} visualizações</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Share2 className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-500 mr-2">Compartilhar:</span>
            <a
              href={shareLinks.facebook}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800"
            >
              <Facebook className="w-5 h-5" />
            </a>
            <a
              href={shareLinks.twitter}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-600"
            >
              <Twitter className="w-5 h-5" />
            </a>
            <a
              href={shareLinks.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-700 hover:text-blue-900"
            >
              <Linkedin className="w-5 h-5" />
            </a>
          </div>
        </div>
      </header>

      {/* Featured Image */}
      {article.featuredImage && (
        <div className="mb-8">
          <img
            src={article.featuredImage}
            alt={article.featuredImageAlt || article.title}
            className="w-full h-auto rounded-lg shadow-lg"
          />
          {article.featuredImageAlt && (
            <p className="text-sm text-gray-500 mt-2 text-center italic">
              {article.featuredImageAlt}
            </p>
          )}
        </div>
      )}

      {/* Article Summary */}
      {article.summary && (
        <div className="bg-gray-50 border-l-4 border-primary-500 p-6 mb-8">
          <p className="text-lg text-gray-700 font-medium leading-relaxed">
            {article.summary}
          </p>
        </div>
      )}

      {/* Article Content */}
      <div className="prose prose-lg max-w-none">
        <div 
          className="text-gray-800 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: article.content.replace(/\n/g, '<br>') }}
        />
      </div>

      {/* Article Tags */}
      {article.tags && article.tags.length > 0 && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Tags:</h3>
          <div className="flex flex-wrap gap-2">
            {article.tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 cursor-pointer"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Share Section */}
      <div className="mt-8 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-center space-x-4">
          <span className="text-gray-600">Gostou? Compartilhe:</span>
          <div className="flex space-x-3">
            <a
              href={shareLinks.facebook}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Facebook className="w-4 h-4" />
              <span>Facebook</span>
            </a>
            <a
              href={shareLinks.twitter}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 px-4 py-2 bg-blue-400 text-white rounded-md hover:bg-blue-500 transition-colors"
            >
              <Twitter className="w-4 h-4" />
              <span>Twitter</span>
            </a>
            <a
              href={shareLinks.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 px-4 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-800 transition-colors"
            >
              <Linkedin className="w-4 h-4" />
              <span>LinkedIn</span>
            </a>
          </div>
        </div>
      </div>
    </article>
  )
}

export default ArticleView
