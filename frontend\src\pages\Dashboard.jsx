import { useQuery } from 'react-query'
import { articleService } from '../services/articleService'
import { categoryService } from '../services/categoryService'
import { FileText, FolderOpen, Eye, TrendingUp } from 'lucide-react'
import LoadingSpinner from '../components/LoadingSpinner'

const Dashboard = () => {
  const { data: articlesData, isLoading: articlesLoading } = useQuery(
    'dashboard-articles',
    () => articleService.getArticles({ limit: 5 })
  )

  const { data: categoriesData, isLoading: categoriesLoading } = useQuery(
    'dashboard-categories',
    () => categoryService.getCategories({ limit: 10 })
  )

  const isLoading = articlesLoading || categoriesLoading

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const articles = articlesData?.data?.articles || []
  const categories = categoriesData?.data?.categories || []
  const totalArticles = articlesData?.data?.pagination?.total || 0
  const totalCategories = categoriesData?.data?.pagination?.total || 0

  const stats = [
    {
      name: 'Total de Artigos',
      value: totalArticles,
      icon: FileText,
      color: 'bg-blue-500'
    },
    {
      name: 'Categorias',
      value: totalCategories,
      icon: FolderOpen,
      color: 'bg-green-500'
    },
    {
      name: 'Artigos Publicados',
      value: articles.filter(a => a.status === 'published').length,
      icon: Eye,
      color: 'bg-purple-500'
    },
    {
      name: 'Rascunhos',
      value: articles.filter(a => a.status === 'draft').length,
      icon: TrendingUp,
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Visão geral do seu portal de notícias</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.name} className="card">
              <div className="card-content">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-md ${stat.color} flex items-center justify-center`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Articles */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Artigos Recentes</h3>
          </div>
          <div className="card-content">
            {articles.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                Nenhum artigo encontrado
              </p>
            ) : (
              <div className="space-y-3">
                {articles.slice(0, 5).map((article) => (
                  <div key={article.id} className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className={`w-2 h-2 rounded-full ${
                        article.status === 'published' ? 'bg-green-500' :
                        article.status === 'draft' ? 'bg-yellow-500' : 'bg-gray-500'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {article.title}
                      </p>
                      <p className="text-sm text-gray-500">
                        {article.category?.name} • {article.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Categories */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Categorias</h3>
          </div>
          <div className="card-content">
            {categories.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                Nenhuma categoria encontrada
              </p>
            ) : (
              <div className="space-y-3">
                {categories.slice(0, 5).map((category) => (
                  <div key={category.id} className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: category.color || '#6B7280' }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {category.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {category.articlesCount || 0} artigos
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
