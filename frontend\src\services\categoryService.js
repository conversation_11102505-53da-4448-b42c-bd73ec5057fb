import api from './api'

class CategoryService {
  async getCategories(params = {}) {
    const response = await api.get('/categories', { params })
    return response
  }

  async getCategoryById(id) {
    const response = await api.get(`/categories/${id}`)
    return response.data.category
  }

  async createCategory(categoryData) {
    const response = await api.post('/categories', categoryData)
    return response
  }

  async updateCategory(id, categoryData) {
    const response = await api.put(`/categories/${id}`, categoryData)
    return response
  }

  async deleteCategory(id) {
    const response = await api.delete(`/categories/${id}`)
    return response
  }
}

export const categoryService = new CategoryService()
