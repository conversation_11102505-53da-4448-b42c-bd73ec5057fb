const { sequelize } = require('../models');
const logger = require('../utils/logger');

const migrate = async () => {
  try {
    logger.info('Iniciando migração do banco de dados...');
    
    // Testar conexão
    await sequelize.authenticate();
    logger.info('Conexão com banco de dados estabelecida.');

    // Sincronizar modelos
    await sequelize.sync({ force: false, alter: true });
    logger.info('Migração concluída com sucesso!');

    process.exit(0);
  } catch (error) {
    logger.error('Erro na migração:', { error: error.message });
    process.exit(1);
  }
};

// Executar migração se chamado diretamente
if (require.main === module) {
  migrate();
}

module.exports = migrate;
