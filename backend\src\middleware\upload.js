const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// Configuração do multer para upload de imagens
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.ALLOWED_FILE_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Tipo de arquivo não permitido. Apenas imagens são aceitas.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB default
  }
});

// Middleware para processar e salvar imagens
const processImage = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo foi enviado'
      });
    }

    const uploadPath = process.env.UPLOAD_PATH || './uploads';
    
    // Criar diretório se não existir
    try {
      await fs.access(uploadPath);
    } catch {
      await fs.mkdir(uploadPath, { recursive: true });
    }

    const fileExtension = path.extname(req.file.originalname);
    const fileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(uploadPath, fileName);

    // Processar imagem com Sharp
    let processedImage = sharp(req.file.buffer);

    // Redimensionar se necessário (máximo 1920x1080)
    const metadata = await processedImage.metadata();
    if (metadata.width > 1920 || metadata.height > 1080) {
      processedImage = processedImage.resize(1920, 1080, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }

    // Otimizar qualidade
    if (req.file.mimetype === 'image/jpeg') {
      processedImage = processedImage.jpeg({ quality: 85 });
    } else if (req.file.mimetype === 'image/png') {
      processedImage = processedImage.png({ quality: 85 });
    } else if (req.file.mimetype === 'image/webp') {
      processedImage = processedImage.webp({ quality: 85 });
    }

    // Salvar arquivo processado
    await processedImage.toFile(filePath);

    // Adicionar informações do arquivo ao request
    req.uploadedFile = {
      filename: fileName,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: (await fs.stat(filePath)).size,
      path: filePath,
      url: `/uploads/${fileName}`
    };

    next();
  } catch (error) {
    console.error('Erro no processamento da imagem:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao processar imagem',
      error: error.message
    });
  }
};

// Middleware para deletar arquivo
const deleteFile = async (filePath) => {
  try {
    const fullPath = path.join(process.env.UPLOAD_PATH || './uploads', path.basename(filePath));
    await fs.unlink(fullPath);
    return true;
  } catch (error) {
    console.error('Erro ao deletar arquivo:', error);
    return false;
  }
};

module.exports = {
  upload: upload.single('image'),
  processImage,
  deleteFile
};
