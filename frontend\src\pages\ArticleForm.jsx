import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useQuery } from 'react-query'
import { articleService } from '../services/articleService'
import { categoryService } from '../services/categoryService'
import LoadingSpinner from '../components/LoadingSpinner'
import toast from 'react-hot-toast'

const ArticleForm = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)

  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    summary: '',
    content: '',
    categoryId: '',
    status: 'draft',
    isFeatured: false,
    isBreaking: false
  })
  const [isLoading, setIsLoading] = useState(false)

  const { data: categoriesData } = useQuery(
    'categories',
    () => categoryService.getCategories({ active: true })
  )

  const { data: articleData, isLoading: articleLoading } = useQuery(
    ['article', id],
    () => articleService.getArticleById(id),
    { enabled: isEditing }
  )

  useEffect(() => {
    if (isEditing && articleData) {
      setFormData({
        title: articleData.title || '',
        subtitle: articleData.subtitle || '',
        summary: articleData.summary || '',
        content: articleData.content || '',
        categoryId: articleData.categoryId || '',
        status: articleData.status || 'draft',
        isFeatured: articleData.isFeatured || false,
        isBreaking: articleData.isBreaking || false
      })
    }
  }, [isEditing, articleData])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (isEditing) {
        await articleService.updateArticle(id, formData)
        toast.success('Artigo atualizado com sucesso!')
      } else {
        await articleService.createArticle(formData)
        toast.success('Artigo criado com sucesso!')
      }
      navigate('/admin/articles')
    } catch (error) {
      toast.error(error.message || 'Erro ao salvar artigo')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  if (isEditing && articleLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const categories = categoriesData?.data?.categories || []

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditing ? 'Editar Artigo' : 'Novo Artigo'}
        </h1>
        <p className="text-gray-600">
          {isEditing ? 'Edite as informações do artigo' : 'Crie um novo artigo para o portal'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Informações Básicas</h3>
          </div>
          <div className="card-content space-y-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Título *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                required
                className="input mt-1"
                value={formData.title}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="subtitle" className="block text-sm font-medium text-gray-700">
                Subtítulo
              </label>
              <input
                type="text"
                id="subtitle"
                name="subtitle"
                className="input mt-1"
                value={formData.subtitle}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="summary" className="block text-sm font-medium text-gray-700">
                Resumo
              </label>
              <textarea
                id="summary"
                name="summary"
                rows={3}
                className="textarea mt-1"
                value={formData.summary}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700">
                Categoria *
              </label>
              <select
                id="categoryId"
                name="categoryId"
                required
                className="input mt-1"
                value={formData.categoryId}
                onChange={handleChange}
              >
                <option value="">Selecione uma categoria</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Conteúdo</h3>
          </div>
          <div className="card-content">
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                Conteúdo *
              </label>
              <textarea
                id="content"
                name="content"
                rows={10}
                required
                className="textarea mt-1"
                value={formData.content}
                onChange={handleChange}
                placeholder="Digite o conteúdo do artigo..."
              />
              <p className="text-sm text-gray-500 mt-1">
                Em breve será integrado o editor WYSIWYG TinyMCE
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Configurações</h3>
          </div>
          <div className="card-content space-y-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                id="status"
                name="status"
                className="input mt-1"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="draft">Rascunho</option>
                <option value="published">Publicado</option>
                <option value="archived">Arquivado</option>
              </select>
            </div>

            <div className="flex items-center space-x-6">
              <div className="flex items-center">
                <input
                  id="isFeatured"
                  name="isFeatured"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  checked={formData.isFeatured}
                  onChange={handleChange}
                />
                <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
                  Artigo em destaque
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="isBreaking"
                  name="isBreaking"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  checked={formData.isBreaking}
                  onChange={handleChange}
                />
                <label htmlFor="isBreaking" className="ml-2 block text-sm text-gray-900">
                  Notícia urgente
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/admin/articles')}
            className="btn-outline"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary"
          >
            {isLoading ? (
              <LoadingSpinner size="sm" />
            ) : (
              isEditing ? 'Atualizar' : 'Criar'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

export default ArticleForm
