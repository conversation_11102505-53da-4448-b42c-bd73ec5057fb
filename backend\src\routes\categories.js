const express = require('express');
const router = express.Router();
const categoryController = require('../controllers/categoryController');
const { authenticateToken, requireEditor } = require('../middleware/auth');
const { validateCategory, validateUUID, validatePagination } = require('../middleware/validation');

// Rotas públicas
router.get('/', validatePagination, categoryController.getCategories);
router.get('/:id', validateUUID, categoryController.getCategoryById);

// Rotas protegidas
router.post('/', authenticateToken, requireEditor, validateCategory, categoryController.createCategory);
router.put('/:id', authenticateToken, requireEditor, validateUUID, validateCategory, categoryController.updateCategory);
router.delete('/:id', authenticateToken, requireEditor, validateUUID, categoryController.deleteCategory);

module.exports = router;
