{"timestamp":"2025-06-05T18:17:26.708Z","level":"INFO","message":"Iniciando migração do banco de dados..."}
{"timestamp":"2025-06-05T18:18:52.889Z","level":"INFO","message":"Iniciando migração do banco de dados..."}
{"timestamp":"2025-06-05T18:18:52.897Z","level":"INFO","message":"Conexão com banco de dados estabelecida."}
{"timestamp":"2025-06-05T18:19:13.251Z","level":"INFO","message":"Iniciando migração do banco de dados..."}
{"timestamp":"2025-06-05T18:19:13.258Z","level":"INFO","message":"Conexão com banco de dados estabelecida."}
{"timestamp":"2025-06-05T18:19:13.646Z","level":"INFO","message":"Migração concluída com sucesso!"}
{"timestamp":"2025-06-05T18:19:20.514Z","level":"INFO","message":"Iniciando seed do banco de dados..."}
{"timestamp":"2025-06-05T18:19:20.796Z","level":"INFO","message":"Usuário administrador criado: <EMAIL> / admin123"}
{"timestamp":"2025-06-05T18:19:20.805Z","level":"INFO","message":"Categoria criada: Política"}
{"timestamp":"2025-06-05T18:19:20.811Z","level":"INFO","message":"Categoria criada: Economia"}
{"timestamp":"2025-06-05T18:19:20.818Z","level":"INFO","message":"Categoria criada: Esportes"}
{"timestamp":"2025-06-05T18:19:20.827Z","level":"INFO","message":"Categoria criada: Tecnologia"}
{"timestamp":"2025-06-05T18:19:20.833Z","level":"INFO","message":"Categoria criada: Entretenimento"}
{"timestamp":"2025-06-05T18:19:20.839Z","level":"INFO","message":"Categoria criada: Saúde"}
{"timestamp":"2025-06-05T18:19:20.840Z","level":"INFO","message":"Seed concluído com sucesso!"}
{"timestamp":"2025-06-05T18:20:48.850Z","level":"INFO","message":"Conexão com banco de dados estabelecida."}
{"timestamp":"2025-06-05T18:20:48.856Z","level":"INFO","message":"Servidor rodando na porta 3001"}
{"timestamp":"2025-06-05T18:20:48.856Z","level":"INFO","message":"Ambiente: development"}
{"timestamp":"2025-06-05T18:20:48.857Z","level":"INFO","message":"API disponível em: http://localhost:3001/api"}
